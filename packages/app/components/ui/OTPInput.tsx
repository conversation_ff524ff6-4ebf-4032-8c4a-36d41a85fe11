import type React from 'react';
import { useRef } from 'react';
import { Dimensions, Keyboard, TextInput, View } from 'react-native';
import { ThemedText } from '../ThemedText';

interface OTPInputProps {
  codeLength?: number;
  onCodeFilled?: (code: string) => void;
  code: string[];
  setCode: React.Dispatch<React.SetStateAction<string[]>>;
}

export default function OTPInput({
  codeLength = 4,
  onCodeFilled = () => {},
  code,
  setCode,
}: OTPInputProps) {
  // Create refs for each input
  const inputRefs = useRef<(TextInput | null)[]>([]);

  const { width } = Dimensions.get('window');
  const inputWidth = Math.floor((width - 60) / codeLength);

  // Handle text change in an input
  const handleTextChange = (text: string, index: number) => {
    // Only allow single digits
    if (text.length > 1) {
      //   text = text.charAt(text.length - 1);
      handlePastedText(text);
      return;
    }

    // Update the code array
    const newCode = [...code];
    newCode[index] = text;
    setCode(newCode);

    // If text is entered and not the last input, focus next input
    if (text.length === 1 && index < codeLength - 1) {
      inputRefs.current[index + 1]?.focus();
    }

    // Check if code is complete
    if (newCode.every((digit) => digit !== '') && !newCode.includes('')) {
      onCodeFilled(newCode.join(''));
      Keyboard.dismiss();
    }
  };

  const handleKeyPress = (e: any, index: number) => {
    if (e.nativeEvent.key === 'Backspace' && index > 0 && code[index] === '') {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePastedText = (text: string) => {
    // Filter out non-numeric characters and limit to codeLength
    const cleanedText = text.replace(/[^0-9]/g, '').slice(0, codeLength);

    if (cleanedText.length === 0) return;

    // Create a new code array with the pasted digits
    const newCode = [...code];

    // Fill in the code array with the pasted digits
    for (let i = 0; i < cleanedText.length; i++) {
      newCode[i] = cleanedText[i];
    }

    // Update the state
    setCode(newCode);

    // Focus the appropriate input (either the next empty one or the last one)
    const nextEmptyIndex = newCode.findIndex((digit) => digit === '');
    if (nextEmptyIndex !== -1) {
      inputRefs.current[nextEmptyIndex]?.focus();
    } else {
      // If all inputs are filled, focus the last one
      inputRefs.current[codeLength - 1]?.focus();
    }

    // Check if code is complete
    checkCodeCompletion(newCode);
  };

  const checkCodeCompletion = (codeArray: string[]) => {
    if (codeArray.every((digit) => digit !== '') && !codeArray.includes('')) {
      onCodeFilled(codeArray.join(''));
      Keyboard.dismiss();
    }
  };

  // Render the OTP input fields
  const renderInputs = () => {
    return Array(codeLength)
      .fill(0)
      .map((_, index) => {
        const hasValue = code[index] !== '';

        return (
          <View
            key={index}
            className={`h-16 justify-center items-center rounded-full  ${
              hasValue
                ? 'border border-brand-purple bg-white'
                : ' bg-brand-light/medium'
            }`}
            style={{ width: inputWidth }}
          >
            <TextInput
              ref={(ref) => {
                inputRefs.current[index] = ref;
              }}
              className={`w-full font-Brand-Medium h-full text-center text-2xl ${
                hasValue ? 'text-brand-purple' : 'text-gray-500'
              }`}
              keyboardType="number-pad"
              maxLength={codeLength}
              value={code[index]}
              onChangeText={(text) => handleTextChange(text, index)}
              onKeyPress={(e) => handleKeyPress(e, index)}
              selectionColor={hasValue ? '#00C896' : '#00C896'}
              autoFocus={index === 0}
            />
          </View>
        );
      });
  };

  return (
    <View>
      <ThemedText type="title">Enter The Code</ThemedText>
      <View className="flex-row justify-between mt-4">{renderInputs()}</View>
    </View>
  );
}
